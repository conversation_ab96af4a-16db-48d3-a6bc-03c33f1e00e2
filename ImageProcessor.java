import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.Arrays;

/**
 * 图片像素增强和像素连接处理器
 * 功能：将图片中的灰色或浅黑色标记增强为纯黑色，背景转换为纯白色，并连接断开的黑色线条
 */
public class ImageProcessor {
    
    private static final int DEFAULT_THRESHOLD = 200;
    private static final boolean DEFAULT_CONNECT = true;
    
    public static void main(String[] args) {
        // 解析命令行参数
        String inputPath = null;
        String outputPath = null;
        int threshold = DEFAULT_THRESHOLD;
        boolean connect = DEFAULT_CONNECT;
        
        for (int i = 0; i < args.length; i++) {
            switch (args[i]) {
                case "--input":
                    if (i + 1 < args.length) inputPath = args[++i];
                    break;
                case "--output":
                    if (i + 1 < args.length) outputPath = args[++i];
                    break;
                case "--threshold":
                    if (i + 1 < args.length) threshold = Integer.parseInt(args[++i]);
                    break;
                case "--connect":
                    if (i + 1 < args.length) connect = Boolean.parseBoolean(args[++i]);
                    break;
                case "--help":
                    printUsage();
                    return;
            }
        }
        
        // 检查必要参数
        if (inputPath == null || outputPath == null) {
            System.err.println("错误：缺少必要参数");
            printUsage();
            System.exit(1);
        }
        
        try {
            // 处理图片
            processImage(inputPath, outputPath, threshold, connect);
            System.out.println("图片处理成功：" + outputPath);
        } catch (Exception e) {
            System.err.println("图片处理失败：" + e.getMessage());
            e.printStackTrace();
            System.exit(1);
        }
    }
    
    /**
     * 处理图片
     */
    public static void processImage(String inputPath, String outputPath, int threshold, boolean connect) throws IOException {
        // 读取图片
        BufferedImage inputImage = ImageIO.read(new File(inputPath));
        if (inputImage == null) {
            throw new IOException("无法读取输入图片：" + inputPath);
        }
        
        // 转换为灰度图
        BufferedImage grayscaleImage = convertToGrayscale(inputImage);
        
        // 应用阈值处理
        BufferedImage thresholdImage = applyThreshold(grayscaleImage, threshold);
        
        // 应用像素连接（如果需要）
        if (connect) {
            thresholdImage = applyPixelConnection(thresholdImage);
        }
        
        // 保存结果
        File outputFile = new File(outputPath);
        outputFile.getParentFile().mkdirs(); // 确保输出目录存在
        
        if (!ImageIO.write(thresholdImage, "jpg", outputFile)) {
            throw new IOException("无法保存输出图片：" + outputPath);
        }
    }
    
    /**
     * 转换为灰度图
     */
    private static BufferedImage convertToGrayscale(BufferedImage image) {
        BufferedImage grayscale = new BufferedImage(image.getWidth(), image.getHeight(), BufferedImage.TYPE_BYTE_GRAY);
        grayscale.getGraphics().drawImage(image, 0, 0, null);
        return grayscale;
    }
    
    /**
     * 应用阈值处理
     */
    private static BufferedImage applyThreshold(BufferedImage image, int threshold) {
        int width = image.getWidth();
        int height = image.getHeight();
        BufferedImage result = new BufferedImage(width, height, BufferedImage.TYPE_BYTE_GRAY);
        
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int rgb = image.getRGB(x, y);
                int gray = rgb & 0xFF; // 获取灰度值
                
                // 应用阈值：低于阈值的变为黑色(0)，否则为白色(255)
                int newGray = (gray < threshold) ? 0 : 255;
                int newRgb = (newGray << 16) | (newGray << 8) | newGray;
                result.setRGB(x, y, newRgb);
            }
        }
        
        return result;
    }
    
    /**
     * 应用像素连接
     */
    private static BufferedImage applyPixelConnection(BufferedImage image) {
        int width = image.getWidth();
        int height = image.getHeight();
        BufferedImage result = new BufferedImage(width, height, BufferedImage.TYPE_BYTE_GRAY);
        
        // 复制原图
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                result.setRGB(x, y, image.getRGB(x, y));
            }
        }
        
        // 横向扫描，相差两个像素进行连接
        for (int y = 0; y < height; y++) {
            for (int x = 2; x < width - 2; x++) {
                int left2 = getGrayValue(image, x - 2, y);
                int left1 = getGrayValue(image, x - 1, y);
                int center = getGrayValue(image, x, y);
                int right1 = getGrayValue(image, x + 1, y);
                int right2 = getGrayValue(image, x + 2, y);
                
                // 如果左右都是黑色，中间是白色，则连接
                if (left2 == 0 && left1 == 255 && center == 255 && right1 == 255 && right2 == 0) {
                    result.setRGB(x - 1, y, 0); // 黑色
                    result.setRGB(x, y, 0);     // 黑色
                    result.setRGB(x + 1, y, 0); // 黑色
                }
            }
        }
        
        // 纵向扫描，相差两个像素进行连接
        for (int x = 0; x < width; x++) {
            for (int y = 2; y < height - 2; y++) {
                int top2 = getGrayValue(image, x, y - 2);
                int top1 = getGrayValue(image, x, y - 1);
                int center = getGrayValue(image, x, y);
                int bottom1 = getGrayValue(image, x, y + 1);
                int bottom2 = getGrayValue(image, x, y + 2);
                
                // 如果上下都是黑色，中间是白色，则连接
                if (top2 == 0 && top1 == 255 && center == 255 && bottom1 == 255 && bottom2 == 0) {
                    result.setRGB(x, y - 1, 0); // 黑色
                    result.setRGB(x, y, 0);     // 黑色
                    result.setRGB(x, y + 1, 0); // 黑色
                }
            }
        }
        
        return result;
    }
    
    /**
     * 获取指定位置的灰度值
     */
    private static int getGrayValue(BufferedImage image, int x, int y) {
        int rgb = image.getRGB(x, y);
        return rgb & 0xFF; // 返回灰度值
    }
    
    /**
     * 打印使用说明
     */
    private static void printUsage() {
        System.out.println("图片像素增强和像素连接处理器");
        System.out.println("用法：java ImageProcessor [选项]");
        System.out.println("选项：");
        System.out.println("  --input <文件路径>     输入图片文件路径");
        System.out.println("  --output <文件路径>    输出图片文件路径");
        System.out.println("  --threshold <数值>     阈值（默认：200）");
        System.out.println("  --connect <true|false> 是否使用像素连接（默认：true）");
        System.out.println("  --help                显示此帮助信息");
        System.out.println();
        System.out.println("示例：");
        System.out.println("  java ImageProcessor --input input.jpg --output output.jpg --threshold 200 --connect true");
    }
} 